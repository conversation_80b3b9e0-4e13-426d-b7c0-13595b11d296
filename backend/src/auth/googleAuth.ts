import { OAuth2Client } from 'google-auth-library';
import jwt from 'jsonwebtoken';
import type { User, AuthTokens, GoogleOAuthConfig } from '../../../shared/types.js';
import { UserModel } from '../database/models/User.js';
import * as SharedTypes from '../../../shared/types';
const { AuthError } = SharedTypes;
import {  } from '../../../shared/types.js';

export class GoogleAuthService {
  private client: OAuth2Client;
  private config: GoogleOAuthConfig;

  constructor() {
    this.config = {
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
      redirectUri: `${process.env.BACKEND_URL || 'http://localhost:3001'}/api/auth/google/callback`
    };

    if (!this.config.clientId || !this.config.clientSecret) {
      throw new Error('Google OAuth configuration is missing. Please set GOOGLE_CLIENT_ID and GOOGLE_CLIENT_SECRET environment variables.');
    }

    this.client = new OAuth2Client(
      this.config.clientId,
      this.config.clientSecret,
      this.config.redirectUri
    );
  }

  /**
   * Generate Google OAuth URL for user authentication
   */
  getAuthUrl(): string {
    const scopes = [
      'https://www.googleapis.com/auth/userinfo.email',
      'https://www.googleapis.com/auth/userinfo.profile'
    ];

    return this.client.generateAuthUrl({
      access_type: 'offline',
      scope: scopes,
      include_granted_scopes: true,
      prompt: 'consent'
    });
  }

  /**
   * Exchange authorization code for tokens and user info
   */
  async handleCallback(code: string): Promise<{ user: User; tokens: AuthTokens; isNewUser: boolean }> {
    try {
      // Exchange code for tokens
      const { tokens } = await this.client.getToken(code);
      this.client.setCredentials(tokens);

      // Verify and decode the ID token
      const ticket = await this.client.verifyIdToken({
        idToken: tokens.id_token!,
        audience: this.config.clientId
      });

      const payload = ticket.getPayload();
      if (!payload) {
        throw new AuthError('Invalid Google token payload');
      }

      // Extract user information
      const googleId = payload.sub;
      const email = payload.email!;
      const name = payload.name!;
      // const picture = payload.picture; // Available for future use

      // Find or create user in database
      let user = await UserModel.findByGoogleId(googleId);
      let isNewUser = false;

      if (!user) {
        // Check if user exists with same email
        const existingUser = await UserModel.findByEmail(email);
        if (existingUser) {
          throw new AuthError('An account with this email already exists');
        }

        // Create new user
        user = await UserModel.create({
          googleId,
          email,
          name,
          profileData: {
            preferences: {
              privacy: {
                saveConversations: true,
                shareEmotionData: false
              }
            }
          }
        });
        isNewUser = true;
      } else {
        // Update user info if needed
        if (user.name !== name || user.email !== email) {
          user = await UserModel.update(user.id, { name, email });
          if (!user) {
            throw new AuthError('Failed to update user information');
          }
        }
      }

      // Generate JWT tokens
      const authTokens = this.generateTokens(user);

      return { user, tokens: authTokens, isNewUser };

    } catch (error) {
      console.error('Google OAuth callback error:', error);
      if (error instanceof AuthError) {
        throw error;
      }
      throw new AuthError('Failed to authenticate with Google');
    }
  }

  /**
   * Verify Google ID token (for frontend direct authentication)
   */
  async verifyIdToken(idToken: string): Promise<User> {
    try {
      const ticket = await this.client.verifyIdToken({
        idToken,
        audience: this.config.clientId
      });

      const payload = ticket.getPayload();
      if (!payload) {
        throw new AuthError('Invalid Google ID token');
      }

      const googleId = payload.sub;
      const email = payload.email!;
      const name = payload.name!;

      // Find or create user
      let user = await UserModel.findByGoogleId(googleId);
      
      if (!user) {
        user = await UserModel.create({
          googleId,
          email,
          name
        });
      }

      return user;

    } catch (error) {
      console.error('Google ID token verification error:', error);
      throw new AuthError('Invalid Google ID token');
    }
  }

  /**
   * Generate JWT access and refresh tokens
   */
  generateTokens(user: User): AuthTokens {
    const jwtSecret = process.env.JWT_SECRET!;
    const expiresIn = process.env.JWT_EXPIRES_IN || '7d';

    if (!jwtSecret) {
      throw new Error('JWT_SECRET environment variable is required');
    }

    const payload = {
      userId: user.id,
      email: user.email,
      name: user.name
    };

    const accessToken = jwt.sign(payload, jwtSecret, {
      expiresIn,
      issuer: 'ora-hume-backend',
      audience: 'ora-hume-frontend'
    } as jwt.SignOptions);

    // For now, we'll use the same token as refresh token
    // In production, you might want separate refresh token logic
    const refreshToken = jwt.sign(
      { ...payload, type: 'refresh' }, 
      jwtSecret, 
      { 
        expiresIn: '30d',
        issuer: 'ora-hume-backend',
        audience: 'ora-hume-frontend'
      }
    );

    return {
      accessToken,
      refreshToken,
      expiresIn: this.getTokenExpirationTime(expiresIn)
    };
  }

  /**
   * Verify JWT token
   */
  verifyToken(token: string): any {
    try {
      const jwtSecret = process.env.JWT_SECRET!;
      return jwt.verify(token, jwtSecret, {
        issuer: 'ora-hume-backend',
        audience: 'ora-hume-frontend'
      });
    } catch (error) {
      throw new AuthError('Invalid or expired token');
    }
  }

  /**
   * Refresh access token using refresh token
   */
  async refreshToken(refreshToken: string): Promise<AuthTokens> {
    try {
      const decoded = this.verifyToken(refreshToken);
      
      if (decoded.type !== 'refresh') {
        throw new AuthError('Invalid refresh token');
      }

      // Get fresh user data
      const user = await UserModel.findById(decoded.userId);
      if (!user) {
        throw new AuthError('User not found');
      }

      // Generate new tokens
      return this.generateTokens(user);

    } catch (error) {
      if (error instanceof AuthError) {
        throw error;
      }
      throw new AuthError('Failed to refresh token');
    }
  }

  /**
   * Convert expiration string to seconds
   */
  private getTokenExpirationTime(expiresIn: string): number {
    const match = expiresIn.match(/^(\d+)([smhd])$/);
    if (!match) return 3600; // Default 1 hour

    const value = parseInt(match[1]!);
    const unit = match[2]!

    switch (unit) {
      case 's': return value;
      case 'm': return value * 60;
      case 'h': return value * 3600;
      case 'd': return value * 86400;
      default: return 3600;
    }
  }
}
