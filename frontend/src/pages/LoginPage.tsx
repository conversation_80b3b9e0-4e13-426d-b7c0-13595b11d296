import { useState, useEffect } from 'react'
import { useAuth } from '../contexts/AuthContext'
import { LoadingSpinner } from '../components/ui/LoadingSpinner'

declare global {
  interface Window {
    google: any
  }
}

export default function LoginPage() {
  const [isLoading, setIsLoading] = useState(false)
  const { login } = useAuth()

  useEffect(() => {
    // Initialize Google One Tap when the component mounts
    initializeGoogleOneTap()
  }, [])

  const initializeGoogleOneTap = () => {
    console.log('🔍 Initializing Google One Tap...')
    console.log('🔍 Client ID:', import.meta.env.VITE_GOOGLE_CLIENT_ID)
    console.log('🔍 Window.google available:', !!window.google)

    if (typeof window !== 'undefined' && window.google) {
      try {
        console.log('🔍 Google accounts.id available:', !!window.google.accounts?.id)

        window.google.accounts.id.initialize({
          client_id: import.meta.env.VITE_GOOGLE_CLIENT_ID,
          callback: handleGoogleResponse,
          auto_select: false,
          cancel_on_tap_outside: true,
        })

        console.log('✅ Google One Tap initialized successfully')

        // Render the One Tap prompt
        const buttonElement = document.getElementById('google-signin-button')
        console.log('🔍 Button element found:', !!buttonElement)

        if (buttonElement) {
          window.google.accounts.id.renderButton(buttonElement, {
            theme: 'filled_blue',
            size: 'large',
            width: '100%',
            text: 'signin_with',
            shape: 'rectangular',
          })
          console.log('✅ Google Sign-In button rendered')
        }

        // Show One Tap prompt
        window.google.accounts.id.prompt()
        console.log('✅ Google One Tap prompt shown')
      } catch (error) {
        console.error('❌ Failed to initialize Google One Tap:', error)
        // Fallback to OAuth redirect flow
        setFallbackMode(true)
      }
    } else {
      console.log('⏳ Google script not loaded yet, retrying...')
      // Google script not loaded yet, try again after a short delay
      setTimeout(initializeGoogleOneTap, 100)
    }
  }

  const handleGoogleResponse = async (response: any) => {
    console.log('🔍 Google response received:', response)
    try {
      setIsLoading(true)
      console.log('🔍 Attempting login with credential...')
      await login(response.credential)
      console.log('✅ Login successful')
    } catch (error) {
      console.error('❌ Google sign-in error:', error)
      // Fallback to OAuth redirect flow on error
      console.log('🔄 Falling back to OAuth redirect flow')
      initiateOAuth2Flow()
    } finally {
      setIsLoading(false)
    }
  }

  const [fallbackMode, setFallbackMode] = useState(false)

  const initiateOAuth2Flow = () => {
    // Redirect to backend OAuth2 endpoint as fallback
    const authUrl = `${import.meta.env.VITE_API_URL}/api/auth/google`
    window.location.href = authUrl
  }

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Enhanced background with warmer center - matching onboarding style */}
      <div className="absolute inset-0 bg-gradient-to-br from-dark-900 via-dark-800 to-dark-700"></div>

      {/* Central warm glow effect with the specific warm center color #671700 */}
      <div className="absolute inset-0 bg-gradient-radial from-warm-center/30 via-dark-700/20 to-transparent"></div>
      <div className="absolute inset-0 bg-gradient-radial from-warm-center/20 via-transparent to-transparent"></div>

      {/* Additional subtle warm tones */}
      <div className="absolute inset-0 bg-gradient-to-br from-transparent via-warm-center/10 to-transparent"></div>

      <div className="relative z-10 flex items-center justify-center min-h-screen p-6">
        <div className="text-center space-y-12 max-w-md mx-auto">
          {/* Logo/Icon - Matching onboarding style */}
          <div className="flex justify-center mb-12">
            <div className="w-16 h-16 bg-gradient-to-br from-aura to-sunbeam rounded-full flex items-center justify-center shadow-lg">
              <span className="text-2xl font-bold text-white">O</span>
            </div>
          </div>

          {/* Welcome message */}
          <div className="space-y-8">
            <h1 className="text-4xl md:text-5xl font-light text-white leading-tight tracking-wide whitespace-nowrap">
              Hey there, I'm Ora
            </h1>
            <p className="text-lg text-white/80 leading-relaxed font-light">
              Your personal AI friend there for you 24/7
            </p>
          </div>

          {/* Google Sign-In */}
          <div className="space-y-6 pt-16">
            {isLoading ? (
              <div className="flex items-center justify-center w-full py-5 px-8 bg-gradient-to-r from-dark-500 to-dark-600 rounded-3xl border border-dark-400/50">
                <LoadingSpinner size="sm" />
                <span className="ml-2 text-white">Signing in...</span>
              </div>
            ) : (
              <div className="space-y-4">
                {/* Google One Tap Sign-In Button */}
                <div id="google-signin-button" className="w-full flex justify-center"></div>

                {/* Fallback button if Google One Tap fails */}
                {fallbackMode && (
                  <button
                    onClick={initiateOAuth2Flow}
                    className="w-full bg-gradient-to-r from-dark-500 to-dark-600 text-white py-5 px-8 rounded-3xl text-lg font-medium hover:from-dark-400 hover:to-dark-500 transition-all duration-300 shadow-xl border border-dark-400/50 backdrop-blur-sm"
                  >
                    Sign in with Google
                  </button>
                )}
              </div>
            )}
          </div>

          {/* Terms and privacy */}
          {/* <div className="text-center pt-8">
            <p className="text-xs text-white/60">
              By continuing, you agree to our{' '}
              <button className="text-aura-300 hover:underline font-medium">
                Terms of Service
              </button>{' '}
              and{' '}
              <button className="text-aura-300 hover:underline font-medium">
                Privacy Policy
              </button>
            </p> */}
          {/* </div> */}
        </div>
      </div>
    </div>
  )
}
